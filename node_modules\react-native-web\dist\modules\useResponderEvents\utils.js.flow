/**
 * Copyright (c) <PERSON>
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import isSelectionValid from '../../modules/isSelectionValid';
const keyName = '__reactResponderId';
declare function getEventPath(domEvent: any): Array<any>;
declare function composedPathFallback(target: any): Array<any>;
/**
 * Retrieve the responderId from a host node
 */
declare function getResponderId(node: any): ?number;
/**
 * Store the responderId on a host node
 */
declare export function setResponderId(node: any, id: number): any;
/**
 * Filter the event path to contain only the nodes attached to the responder system
 */
declare export function getResponderPaths(domEvent: any): {|
  idPath: Array<number>,
  nodePath: Array<any>,
|};
/**
 * Walk the paths and find the first common ancestor
 */
declare export function getLowestCommonAncestor(pathA: Array<any>, pathB: Array<any>): any;
/**
 * Determine whether any of the active touches are within the current responder.
 * This cannot rely on W3C `targetTouches`, as neither IE11 nor Safari implement it.
 */
declare export function hasTargetTouches(target: any, touches: any): boolean;
/**
 * Ignore 'selectionchange' events that don't correspond with a person's intent to
 * select text.
 */
declare export function hasValidSelection(domEvent: any): boolean;
/**
 * Events are only valid if the primary button was used without specific modifier keys.
 */
declare export function isPrimaryPointerDown(domEvent: any): boolean;