/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import invariant from 'fbjs/lib/invariant';
type Content = {
  title?: string,
  message?: string,
  url: string,
} | {
  title?: string,
  message: string,
  url?: string,
};
declare class Share {
  static share(content: Content, options: Object): Promise<Object>,
  static sharedAction(): string,
  static dismissedAction(): string,
}
export default Share;