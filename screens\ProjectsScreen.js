import React, { useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, Modal, TextInput, StyleSheet } from 'react-native';

export default function ProjectsScreen({ navigation }) {
  const [projects, setProjects] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [newProject, setNewProject] = useState({ name: '', clientName: '', description: '' });

  const addProject = () => {
    if (newProject.name.trim() === '') return;
    
    setProjects([...projects, {
      id: Date.now().toString(),
      name: newProject.name,
      clientName: newProject.clientName,
      description: newProject.description,
      createdAt: new Date(),
      totalTime: 0, // in seconds
    }]);
    
    setNewProject({ name: '', clientName: '', description: '' });
    setModalVisible(false);
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={projects}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity 
            style={styles.projectCard}
            onPress={() => navigation.navigate('Timer', { project: item })}
          >
            <Text style={styles.projectName}>{item.name}</Text>
            <Text style={styles.clientName}>Client: {item.clientName}</Text>
            <Text style={styles.timeTracked}>
              Time: {Math.floor(item.totalTime / 3600)}h {Math.floor((item.totalTime % 3600) / 60)}m
            </Text>
          </TouchableOpacity>
        )}
      />
      
      <TouchableOpacity 
        style={styles.addButton} 
        onPress={() => setModalVisible(true)}
      >
        <Text style={styles.addButtonText}>+ New Project</Text>
      </TouchableOpacity>

      {/* New Project Modal */}
      <Modal visible={modalVisible} animationType="slide">
        <View style={styles.modalContainer}>
          <Text style={styles.modalTitle}>Add New Project</Text>
          
          <TextInput
            style={styles.input}
            placeholder="Project Name"
            value={newProject.name}
            onChangeText={(text) => setNewProject({...newProject, name: text})}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Client Name"
            value={newProject.clientName}
            onChangeText={(text) => setNewProject({...newProject, clientName: text})}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Description"
            multiline
            value={newProject.description}
            onChangeText={(text) => setNewProject({...newProject, description: text})}
          />
          
          <View style={styles.buttonRow}>
            <TouchableOpacity 
              style={[styles.button, styles.cancelButton]} 
              onPress={() => setModalVisible(false)}
            >
              <Text style={styles.buttonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.button, styles.saveButton]} 
              onPress={addProject}
            >
              <Text style={styles.buttonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  // Other styles omitted for brevity
});