/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

import AnimatedNode from './AnimatedNode';
import AnimatedTransform from './AnimatedTransform';
import AnimatedWithChildren from './AnimatedWithChildren';
import NativeAnimatedHelper from '../NativeAnimatedHelper';
import StyleSheet from '../../../../exports/StyleSheet';
const flattenStyle = StyleSheet.flatten;
declare function createAnimatedStyle(inputStyle: any): Object;
declare class AnimatedStyle extends AnimatedWithChildren {
  _inputStyle: any,
  _style: Object,
  constructor(style: any): any,
  _walkStyleAndGetValues(style: any): any,
  __getValue(): Array<Object>,
  _walkStyleAndGetAnimatedValues(style: any): any,
  __getAnimatedValue(): Object,
  __attach(): void,
  __detach(): void,
  __makeNative(): any,
  __getNativeConfig(): Object,
}
export default AnimatedStyle;