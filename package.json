{"name": "billable-hours-business", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~3.1.3", "@expo/vector-icons": "^14.0.0", "@react-navigation/bottom-tabs": "^6.0.0", "@react-navigation/native": "^6.0.0", "expo": "~50.0.0", "react": "18.2.0", "react-dom": "^18.3.1", "react-native": "0.73.6", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}