/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

import Animation from './Animation';
import { shouldUseNativeDriver } from '../NativeAnimatedHelper';
import type AnimatedValue from '../nodes/AnimatedValue';
import type { AnimationConfig, EndCallback } from './Animation';
export type DecayAnimationConfig = {
  ...AnimationConfig,
  velocity: number | {
    x: number,
    y: number,
    ...
  },
  deceleration?: number,
};
export type DecayAnimationConfigSingle = {
  ...AnimationConfig,
  velocity: number,
  deceleration?: number,
};
declare class DecayAnimation extends Animation {
  _startTime: number,
  _lastValue: number,
  _fromValue: number,
  _deceleration: number,
  _velocity: number,
  _onUpdate: (value: number) => void,
  _animationFrame: any,
  _useNativeDriver: boolean,
  constructor(config: DecayAnimationConfigSingle): any,
  __getNativeAnimationConfig(): {|
    deceleration: number,
    iterations: number,
    type: $TEMPORARY$string<'decay'>,
    velocity: number,
  |},
  start(fromValue: number, onUpdate: (value: number) => void, onEnd: ?EndCallback, previousAnimation: ?Animation, animatedValue: AnimatedValue): void,
  onUpdate(): void,
  stop(): void,
}
export default DecayAnimation;