import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet } from 'react-native';

export default function TimerScreen({ route, navigation }) {
  const [selectedProject, setSelectedProject] = useState(route.params?.project || null);
  const [isRunning, setIsRunning] = useState(false);
  const [time, setTime] = useState(0);
  const [notes, setNotes] = useState('');
  
  const timerRef = useRef(null);

  useEffect(() => {
    if (route.params?.project) {
      setSelectedProject(route.params.project);
    }
  }, [route.params?.project]);

  useEffect(() => {
    if (isRunning) {
      timerRef.current = setInterval(() => {
        setTime(prevTime => prevTime + 1);
      }, 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isRunning]);

  const toggleTimer = () => {
    setIsRunning(!isRunning);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setTime(0);
  };

  const saveSession = () => {
    // Here you would save the session to your data store
    // For now, we'll just reset
    resetTimer();
    setNotes('');
  };

  const formatTime = (seconds) => {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <View style={styles.container}>
      {selectedProject ? (
        <>
          <Text style={styles.projectName}>{selectedProject.name}</Text>
          <Text style={styles.clientName}>Client: {selectedProject.clientName}</Text>
          
          <View style={styles.timerContainer}>
            <Text style={styles.timerDisplay}>{formatTime(time)}</Text>
            
            <View style={styles.buttonRow}>
              <TouchableOpacity 
                style={[styles.button, isRunning ? styles.stopButton : styles.startButton]} 
                onPress={toggleTimer}
              >
                <Text style={styles.buttonText}>{isRunning ? 'Stop' : 'Start'}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.button, styles.resetButton]} 
                onPress={resetTimer}
              >
                <Text style={styles.buttonText}>Reset</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          <TextInput
            style={styles.notesInput}
            placeholder="Add notes about this work session..."
            multiline
            value={notes}
            onChangeText={setNotes}
          />
          
          <TouchableOpacity 
            style={[styles.button, styles.saveButton, !time && styles.disabledButton]} 
            onPress={saveSession}
            disabled={!time}
          >
            <Text style={styles.buttonText}>Save Session</Text>
          </TouchableOpacity>
        </>
      ) : (
        <View style={styles.noProjectContainer}>
          <Text style={styles.noProjectText}>No project selected</Text>
          <TouchableOpacity 
            style={styles.selectProjectButton} 
            onPress={() => navigation.navigate('Projects')}
          >
            <Text style={styles.buttonText}>Select a Project</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  // Other styles omitted for brevity
});