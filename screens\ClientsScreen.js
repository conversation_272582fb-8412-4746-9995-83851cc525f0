import React, { useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, Modal, TextInput, StyleSheet } from 'react-native';

export default function ClientsScreen() {
  const [clients, setClients] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [newClient, setNewClient] = useState({ name: '', email: '', phone: '', notes: '' });

  const addClient = () => {
    if (newClient.name.trim() === '') return;
    
    setClients([...clients, {
      id: Date.now().toString(),
      name: newClient.name,
      email: newClient.email,
      phone: newClient.phone,
      notes: newClient.notes,
    }]);
    
    setNewClient({ name: '', email: '', phone: '', notes: '' });
    setModalVisible(false);
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={clients}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <View style={styles.clientCard}>
            <Text style={styles.clientName}>{item.name}</Text>
            <Text style={styles.clientDetail}>{item.email}</Text>
            <Text style={styles.clientDetail}>{item.phone}</Text>
          </View>
        )}
      />
      
      <TouchableOpacity 
        style={styles.addButton} 
        onPress={() => setModalVisible(true)}
      >
        <Text style={styles.addButtonText}>+ New Client</Text>
      </TouchableOpacity>

      {/* New Client Modal */}
      <Modal visible={modalVisible} animationType="slide">
        <View style={styles.modalContainer}>
          <Text style={styles.modalTitle}>Add New Client</Text>
          
          <TextInput
            style={styles.input}
            placeholder="Client Name"
            value={newClient.name}
            onChangeText={(text) => setNewClient({...newClient, name: text})}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Email"
            keyboardType="email-address"
            value={newClient.email}
            onChangeText={(text) => setNewClient({...newClient, email: text})}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Phone"
            keyboardType="phone-pad"
            value={newClient.phone}
            onChangeText={(text) => setNewClient({...newClient, phone: text})}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Notes"
            multiline
            value={newClient.notes}
            onChangeText={(text) => setNewClient({...newClient, notes: text})}
          />
          
          <View style={styles.buttonRow}>
            <TouchableOpacity 
              style={[styles.button, styles.cancelButton]} 
              onPress={() => setModalVisible(false)}
            >
              <Text style={styles.buttonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.button, styles.saveButton]} 
              onPress={addClient}
            >
              <Text style={styles.buttonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  // Styles omitted for brevity
});