{"version": 3, "file": "messageSocket.js", "sourceRoot": "", "sources": ["../src/messageSocket.ts"], "names": [], "mappings": "AAAA,wBAAwB;AAExB,yEAAyE;AACzE,+CAA+C;AAE/C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AACtE,MAAM,aAAa,GAAG,IAAI,SAAS,CAAC,GAAG,QAAQ,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,CAAC;AACrF,aAAa,CAAC,SAAS,GAAG,CAAC,OAAO,EAAE,EAAE;IACpC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9C,QAAQ,IAAI,CAAC,MAAM,EAAE;QACnB,KAAK,gBAAgB;YACnB,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;gBACxB,KAAK,QAAQ;oBACX,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;oBACzB,MAAM;aACT;YACD,MAAM;QACR,KAAK,QAAQ;YACX,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACzB,MAAM;QACR,KAAK,SAAS;YACZ,QAAQ;YACR,MAAM;KACT;AACH,CAAC,CAAC", "sourcesContent": ["/* eslint-env browser */\n\n// Setup websocket messages for reloading the page from the command line.\n// This is normally setup on the native client.\n\nconst protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';\nconst messageSocket = new WebSocket(`${protocol}://${window.location.host}/message`);\nmessageSocket.onmessage = (message) => {\n  const data = JSON.parse(String(message.data));\n  switch (data.method) {\n    case 'sendDevCommand':\n      switch (data.params.name) {\n        case 'reload':\n          window.location.reload();\n          break;\n      }\n      break;\n    case 'reload':\n      window.location.reload();\n      break;\n    case 'devMenu':\n      // no-op\n      break;\n  }\n};\n"]}