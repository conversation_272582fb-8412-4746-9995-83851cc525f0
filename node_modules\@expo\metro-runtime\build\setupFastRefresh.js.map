{"version": 3, "file": "setupFastRefresh.js", "sourceRoot": "", "sources": ["../src/setupFastRefresh.ts"], "names": [], "mappings": "AAAA,qDAAqD;AAErD,MAAM,mBAAmB,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC7D,mBAAmB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;AAEjD,MAAM,OAAO,GAAG;IACd,kBAAkB;QAChB,QAAQ,CAAC,MAAM,EAAE,CAAC;IACpB,CAAC;IAED,mCAAmC,EAAE,mBAAmB,CAAC,mCAAmC;IAE5F,qBAAqB,EAAE,mBAAmB,CAAC,qBAAqB;IAEhE,eAAe,EAAE,mBAAmB,CAAC,eAAe;IAEpD,QAAQ,EAAE,mBAAmB,CAAC,QAAQ;IAEtC,mBAAmB;QACjB,IAAI,mBAAmB,CAAC,sBAAsB,EAAE,EAAE;YAChD,QAAQ,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO;SACR;QACD,mBAAmB,CAAC,mBAAmB,EAAE,CAAC;IAC5C,CAAC;CACF,CAAC;AAEF,oFAAoF;AACpF,mFAAmF;AACnF,MAAM,CAAC,CAAC,MAAM,CAAC,uBAAuB,IAAI,EAAE,CAAC,GAAG,gBAAgB,CAAC,GAAG,OAAO,CAAC", "sourcesContent": ["// This needs to run before the renderer initializes.\n\nconst ReactRefreshRuntime = require('react-refresh/runtime');\nReactRefreshRuntime.injectIntoGlobalHook(global);\n\nconst Refresh = {\n  performFullRefresh() {\n    location.reload();\n  },\n\n  createSignatureFunctionForTransform: ReactRefreshRuntime.createSignatureFunctionForTransform,\n\n  isLikelyComponentType: ReactRefreshRuntime.isLikelyComponentType,\n\n  getFamilyByType: ReactRefreshRuntime.getFamilyByType,\n\n  register: ReactRefreshRuntime.register,\n\n  performReactRefresh() {\n    if (ReactRefreshRuntime.hasUnrecoverableErrors()) {\n      location.reload();\n      return;\n    }\n    ReactRefreshRuntime.performReactRefresh();\n  },\n};\n\n// The metro require polyfill can not have dependencies (applies for all polyfills).\n// Expose `Refresh` by assigning it to global to make it available in the polyfill.\nglobal[(global.__METRO_GLOBAL_PREFIX__ || '') + '__ReactRefresh'] = Refresh;\n"]}