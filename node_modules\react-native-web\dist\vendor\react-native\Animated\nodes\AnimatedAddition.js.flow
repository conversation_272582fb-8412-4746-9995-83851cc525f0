/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

import type AnimatedNode from './AnimatedNode';
import AnimatedInterpolation from './AnimatedInterpolation';
import AnimatedValue from './AnimatedValue';
import AnimatedWithChildren from './AnimatedWithChildren';
import type { PlatformConfig } from '../AnimatedPlatformConfig';
import type { InterpolationConfigType } from './AnimatedInterpolation';
declare class AnimatedAddition extends AnimatedWithChildren {
  _a: AnimatedNode,
  _b: AnimatedNode,
  constructor(a: AnimatedNode | number, b: AnimatedNode | number): any,
  __makeNative(platformConfig: ?PlatformConfig): any,
  __getValue(): number,
  interpolate<OutputT: number | string>(config: InterpolationConfigType<OutputT>): AnimatedInterpolation<OutputT>,
  __attach(): void,
  __detach(): void,
  __getNativeConfig(): any,
}
export default AnimatedAddition;