{"version": 3, "file": "setupHMR.js", "sourceRoot": "", "sources": ["../src/setupHMR.ts"], "names": [], "mappings": ";;;;;AAAA,4DAAoC;AAEpC,gDAAgD;AAChD,6EAA6E;AAC7E,IAAI;AACJ,aAAa;AACb,YAAY;AACZ,YAAY;AACZ,aAAa;AACb,WAAW;AACX,aAAa;AACb,sBAAsB;AACtB,gBAAgB;AAChB,aAAa;AACb,uBAAuB;AACvB,6CAA6C;AAC7C,0DAA0D;AAC1D,qBAAqB;AACrB,4BAA4B;AAC5B,sBAAsB;AACtB,6CAA6C;AAC7C,OAAO;AACP,MAAM;AAEN,mBAAS,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,+CAA+C,CAAC,CAAC,CAAC;AAExE,4CAA4C;AAC5C,mBAAS,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC", "sourcesContent": ["import HMRClient from './HMRClient';\n\n// Sets up developer tools for React Native web.\n// We assume full control over the console and send JavaScript logs to Metro.\n// [\n//   'trace',\n//   'info',\n//   'warn',\n//   'error',\n//   'log',\n//   'group',\n//   'groupCollapsed',\n//   'groupEnd',\n//   'debug',\n// ].forEach(level => {\n//   const originalFunction = console[level];\n//   console[level] = function (...args: readonly any[]) {\n//     HMRClient.log(\n//       // @ts-expect-error\n//       level, args);\n//     originalFunction.apply(console, args);\n//   };\n// });\n\nHMRClient.log('log', [`[web] Logs will appear in the browser console`]);\n\n// This is called native on native platforms\nHMRClient.setup({ isEnabled: true });\n"]}