/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import { atomic, classic, inline } from './compiler';
import { createSheet } from './dom';
import { localizeStyle } from 'styleq/transform-localize-style';
import { preprocess } from './preprocess';
import { styleq } from 'styleq';
import { validate } from './validate';
import canUseDOM from '../../modules/canUseDom';
const staticStyleMap: WeakMap<Object, Object> = new WeakMap();
const sheet = createSheet();
const defaultPreprocessOptions = {
  shadow: true,
  textShadow: true
};
declare function customStyleq(styles: any, options: Options): any;
declare function insertRules(compiledOrderedRules: any): any;
declare function compileAndInsertAtomic(style: any): any;
declare function compileAndInsertReset(style: any, key: any): any;
/* ----- API ----- */
const absoluteFillObject = {
  position: 'absolute',
  left: 0,
  right: 0,
  top: 0,
  bottom: 0
};
const absoluteFill = create({
  x: {
    ...absoluteFillObject
  }
}).x;

/**
 * create
 */
declare function create<T: Object>(styles: T): $ReadOnly<T>;
/**
 * compose
 */
declare function compose(style1: any, style2: any): any;
/**
 * flatten
 */
declare function flatten(...styles: any): {
  [key: string]: any
};
/**
 * getSheet
 */
declare function getSheet(): {
  id: string,
  textContent: string,
};
/**
 * resolve
 */
type StyleProps = [string, {
  [key: string]: mixed
} | null];
type Options = {
  shadow?: boolean,
  textShadow?: boolean,
  writingDirection: 'ltr' | 'rtl',
};
declare function StyleSheet(styles: any, options?: Options): StyleProps;
StyleSheet.absoluteFill = absoluteFill;
StyleSheet.absoluteFillObject = absoluteFillObject;
StyleSheet.create = create;
StyleSheet.compose = compose;
StyleSheet.flatten = flatten;
StyleSheet.getSheet = getSheet;
// `hairlineWidth` is not implemented using screen density as browsers may
// round sub-pixel values down to `0`, causing the line not to be rendered.
StyleSheet.hairlineWidth = 1;
if (canUseDOM && window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.resolveRNStyle = StyleSheet.flatten;
}
export type IStyleSheet = {
  absoluteFill: Object,
  absoluteFillObject: Object,
  create: typeof create,
  compose: typeof compose,
  flatten: typeof flatten,
  getSheet: typeof getSheet,
  hairlineWidth: number,
  (styles: $ReadOnlyArray<any>, options?: Options): StyleProps,
};
const stylesheet: IStyleSheet = StyleSheet;
export default stylesheet;