/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import { addEventListener } from '../addEventListener';
import useLayoutEffect from '../useLayoutEffect';
import useStable from '../useStable';
type Callback = null | ((any) => void);
type AddListener = (target: EventTarget, listener: null | ((any) => void)) => () => void;

/**
 * This can be used with any event type include custom events.
 *
 * const click = useEvent('click', options);
 * useEffect(() => {
 *   click.setListener(target, onClick);
 *   return () => click.clear();
 * }).
 */
declare export default function useEvent(eventType: string, options?: ?{
  capture?: boolean,
  passive?: boolean,
  once?: boolean,
}): AddListener;